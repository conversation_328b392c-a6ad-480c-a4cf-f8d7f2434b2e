import { ClientType, Innertube, UniversalCache } from "youtubei.js";

export class YouTube {
    public Status: string;
    public Session: Innertube | undefined;
    
    constructor() {
        this.Status = "INIT"
        this.Session = undefined
    }

    async create() {
        const Session = await Innertube.create({
            cache: new UniversalCache(true),
            client_type: ClientType.WEB_EMBEDDED,
            player_id: "a10d7fcc"
        });

        console.log(`YouTube: Session created`)

        this.Session = Session
    }
}