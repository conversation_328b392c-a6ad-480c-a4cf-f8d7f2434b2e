import YouTube from "./platforms/YouTube";
import VideoInfo from "youtubei.js";
import axios from "axios";

interface ErrorType {
  error: string;
  link?: string;
}

export default class Obsidian {
  public YouTube: YouTube | null;

  constructor() {
    this.YouTube = null;
  }

  initialize() {
    console.log(`Obsidian: init started`);

    const YouTubeClient = new YouTube();
    YouTubeClient.start()

    this.YouTube = YouTubeClient
  }

  identifyUrl(url: string) {
    let Platforms = {
      "youtube.com/watch": "YouTubeVideo",
      "youtu.be": "YouTubeVideo",
      "youtube.com/playlist": "YouTubePlaylist",
      "youtube.com/channel/": "YouTubeChannel",
      "youtube.com/shorts/": "YouTubeShorts",
      "music.youtube.com/watch": "YouTubeMusicVideo",
      "music.youtube.com/playlist": "YouTubeMusicPlaylist",
      "music.youtube.com/channel": "YouTubeMusicChannel",
      "on.soundcloud.com": "SoundCloudTrack",
      "open.spotify.com/track": "SpotifyTrack"
    }

    const finalURL = url.replace("https://", "").replace("http://", "").replace("www.", "").split("?")[0]
    if (finalURL in Platforms) {
      const platform = Platforms[finalURL as keyof typeof Platforms]
      return platform;
    } else {
      const platform = Platforms[url.split("/")[0] as keyof typeof Platforms]
      if (platform) {
        return platform;
      } else {
        if (url.includes("spotify")) {
          const spotify = Platforms[`${url.split("/")[0]}/${url.split("/")[1]}` as keyof typeof Platforms]
          if (spotify) {
            return platform;
          } else {
            return "INVALID_URL";
          }
        }
        return "INVALID_URL";
      }
    }
  }

  async fetchContent(platform: string, contentInfo: { url: string }): Promise<InstanceType<typeof VideoInfo> | ErrorType> {
    switch (platform) {
      case "YouTubeVideo":
        const videoId = new URL(contentInfo.url).searchParams.get("v");

        if(videoId === null) {
          return {
            error: "URL_NOT_PROCESSED",
            link: contentInfo.url
          };
        }

        const info = await axios.get("http://localhost:3000/v1/IdentifyYouTubeVideo?id=" + videoId)

        if(!info) {
          return {
            error: "NO_INFO_RETURNED",
            link: contentInfo.url
          };
        }

        return info as unknown as ErrorType | VideoInfo;
        break;
      default:
        return {
          error: "UNSUPPORTED_PLATFORM",
          link: contentInfo.url
        };
    }
  }
}
