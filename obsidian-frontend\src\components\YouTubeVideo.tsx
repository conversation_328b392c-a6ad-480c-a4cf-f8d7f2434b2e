import { useEffect, useRef, useState } from "react";
import { Help<PERSON>, Player, YTNodes } from "youtubei.js";
import chroma from "chroma-js";
import { motion } from "framer-motion";

import { Vibrant } from "node-vibrant/browser"
import { Button } from "@heroui/button";
import { Avatar, Tooltip } from "@heroui/react";

// YouTube API Response Types
interface YouTubeBasicInfo {
    id: string;
    channel_id: string;
    title: string;
    thumbnail: Array<{ url: string }>;
}

interface YouTubeAuthor {
    name: string;
    thumbnails: Array<{ url: string }>;
    badges: Array<{ icon_type: string }>;
}

interface YouTubeOwner {
    author: YouTubeAuthor;
    subscriber_count: { text: string };
}

interface YouTubeSecondaryInfo {
    owner?: YouTubeOwner;
}

interface YouTubeStreamingFormat {
    itag: number;
    mime_type: string;
    url?: string;
}

interface YouTubeStreamingData {
    formats?: YouTubeStreamingFormat[];
    adaptive_formats?: YouTubeStreamingFormat[];
    expires: string;
}

interface YouTubeVideoData {
    basic_info: YouTubeBasicInfo;
    secondary_info?: YouTubeSecondaryInfo;
    streaming_data?: YouTubeStreamingData;
}

interface YouTubeAPIResponse {
    data: YouTubeVideoData;
    streaming: string;
}


interface YouTubeVideoProps {
    videoData: YouTubeAPIResponse;
    streaming: string;
}

export default function YouTubeVideo({ videoData, streaming }: YouTubeVideoProps) {
    const [videoInfo, setVideoInfo] = useState<YouTubeVideoData>(videoData.data);
    const [imageURL, setImageURL] = useState<string>(
        videoData.data.basic_info.thumbnail[0].url
            .replace("https://i.ytimg.com/vi/", "./api/youtube/thumbnail/")
            .replace("https://i.ytimg.com/vi_webp/", "./api/youtube/thumbnailwebp/")
    );
    const [style, setStyle] = useState<string>("");
    const [midColor, setMidColor] = useState<string>("#000000");

    const videoImageRef = useRef<HTMLImageElement>(null);

    useEffect(() => {
        console.log(videoInfo.secondary_info?.owner?.author.badges);

        Vibrant.from(imageURL).getPalette()
            .then((palette) => {
                const colors = [
                    palette.DarkVibrant?.hex,
                    palette.Vibrant?.hex,
                    palette.LightMuted?.hex
                ].filter(Boolean);

                const gradient = generateNowPlayingGradient(colors);
                setStyle(gradient);
            })
            .catch(console.error);
    }, [imageURL]);

    function ensureMinLightness(hex: string, minLightness = 0.8): string {
        const c = chroma(hex);
        // Get current HSL [hue, saturation, lightness]
        const [h, s, l] = c.hsl();

        // If it's already light enough, return as-is (normalized)
        if (l >= minLightness) {
            return c.hex();
        }

        // Otherwise, create a new color with the same hue & saturation but minLightness
        const adjusted = chroma.hsl(h, s, minLightness);
        return adjusted.hex();
    }

    function generateNowPlayingGradient(colors: string[]) {
        const [dark, mid, light] = colors.map((c, i) => {
            const base = chroma(c);
            return i === 0
                ? base.darken(2)
                : i === 2
                    ? base.brighten(1.5)
                    : base;
        });

        setMidColor(ensureMinLightness(mid.hex()))

        return `${mid.hex() + "20"}`;
    }



    return (
        <div className="w-full h-full absolute bg-black/50 backdrop-blur-xs flex flex-row items-center justify-center">
            <motion.div initial={{ opacity: 0, scale: 0.8 }} animate={{ opacity: 1, scale: 1 }} transition={{ duration: 0.5 }} className="absolute flex flex-col p-4 rounded-medium transition-background backdrop-blur-[2px] bg-clip-padding backdrop-filter backdrop-blur backdrop-saturate-100 backdrop-contrast-100" style={{ background: style ? style : "#0f0f0f60" }}>
                <div className="flex w-full justify-between items-center">
                    <div className="flex flex-row gap-1 items-center pb-2">
                        <svg role="img" className="size-3" viewBox="0 0 24 24" style={{ fill: midColor }} xmlns="http://www.w3.org/2000/svg"><path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" /></svg>
                        <p style={{ color: midColor }} className="text-sm">YouTube Video</p>
                    </div>
                    <div>
                        <Button className="text-white hover:bg-white/40 cursor-pointer pointer-events-auto min-w-0 w-auto h-auto p-1 rounded-full" isIconOnly aria-label="Close" color="default" variant="light">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                            </svg>
                        </Button>
                    </div>
                </div>
                <MoviePlayer ImageURL={imageURL} Response={videoInfo} />
                <div className="flex flex-col pt-2">
                    <h1 className="text-md font-bold" style={{ color: midColor }}>{videoInfo.basic_info.title}</h1>
                    <div className="flex flex-row gap-2 py-1">
                        <div className="w-10 h-10">
                            <Avatar src={videoInfo.secondary_info?.owner?.author.thumbnails[0].url.replace("https://yt3.ggpht.com/", "./api/youtube/userAvatar/")} />
                        </div>
                        <div className="flex flex-col justify-center w-full">
                            <div className="flex flex-row gap-1">
                                <h1 className="text-sm font-bold" style={{ color: midColor }}>{videoInfo.secondary_info?.owner?.author.name}</h1>
                                <div>
                                    {videoInfo.secondary_info?.owner?.author.badges?.map((badge) => {
                                        // @ts-ignore
                                        switch (badge.icon_type) {
                                            case "AUDIO_BADGE":
                                                return (
                                                    <VerifiedArtistBadge Color={midColor} />
                                                )
                                            case "CHECK_CIRCLE_THICK":
                                                return (
                                                    <VerifiedChannelBadge Color={midColor} />
                                                )
                                            default:
                                                return (
                                                    <></>
                                                )
                                        }
                                    })}
                                </div>
                            </div>
                            <p className="text-xs font-bormal" style={{ color: midColor + "cc" }}>{videoInfo.secondary_info?.owner?.subscriber_count.text}</p>
                        </div>
                    </div>
                </div>
            </motion.div>
        </div>
    )
}

export function VerifiedArtistBadge({ Color }: { Color: string }) {
    return (
        <Tooltip content="Official Artist Channel" closeDelay={0} delay={0} showArrow={true}>
            <svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 16 16" width="16" focusable="false" aria-hidden="true" style={{ fill: Color, userSelect: "none", display: "inherit", width: "100%", height: "100%", cursor: "pointer" }}><path clip-rule="evenodd" d="M13.053 5.906a2.1 2.1 0 01.002 4.188 2.1 2.1 0 01-2.963 2.961 2.1 2.1 0 01-4.189.003 2.1 2.1 0 01-2.96-2.964 2.1 2.1 0 01-.002-4.188 2.1 2.1 0 012.962-2.961 2.1 2.1 0 014.189-.001 2.1 2.1 0 012.961 2.962ZM7.999 4v4.668a1.75 1.75 0 101 1.582V6h2V4h-3Z" fill-rule="evenodd"></path></svg>
        </Tooltip>
    )
}

export function VerifiedChannelBadge({ Color }: { Color: string }) {
    return (
        <Tooltip content="Verified" closeDelay={0} delay={0} showArrow={true}>
            <svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 24 24" width="16" focusable="false" aria-hidden="true" style={{ fill: Color, userSelect: "none", display: "inherit", width: "100%", height: "100%", cursor: "pointer" }}><path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zM9.8 17.3l-4.2-4.1L7 11.8l2.8 2.7L17 7.4l1.4 1.4-8.6 8.5z"></path></svg>
        </Tooltip>
    )
}

export function MoviePlayer({ ImageURL, Response }: { ImageURL: string, Response: { data: FullVideoInfo, streaming: string } }) {
    const VideoImageRef = useRef(null);
    const VideoRef = useRef(null);
    const VideoSrc = useRef(null);
    const [PlayerStatus, SetPlayerStatus] = useState("WAITING");

    useEffect(() => {
        console.log(Response.data)

        const Format = Response.data.streaming_data?.formats;
        const VideoFormat = Format?.[0];
        const videoElement = VideoRef.current;

        if (!videoElement || !VideoFormat) return;

        const parsed = new URL(Response.streaming);
        if (parsed.hostname !== "redirector.googlevideo.com") {
          parsed.hostname = "redirector.googlevideo.com";
        }
        
        videoElement.src = parsed

        // Playback status handlers
        videoElement.onplay = () => SetPlayerStatus("PLAYING");
        videoElement.onpause = () => SetPlayerStatus("PAUSED");
        videoElement.addEventListener("waiting", () => SetPlayerStatus("BUFFERING"));
    }, []);


    function StartPlaying() {
        if (PlayerStatus === "WAITING") {
            document.querySelector("video")?.play()
        }
    }

    function HandlePlayButtonClick() {
        document.querySelector("video")?.play()
    }

    function HandlePauseButtonClick() {
        document.querySelector("video")?.pause()
    }

    return (
        <div>
            <div className="relative">
                <div className="absolute top-0 left-0 w-full h-full flex flex-row items-center justify-center">
                    {PlayerStatus === "WAITING" ? (
                        <div onClick={StartPlaying}>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="hover:scale-110 size-12 cursor-pointer">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm14.024-.983a1.125 1.125 0 0 1 0 1.966l-5.603 3.113A1.125 1.125 0 0 1 9 15.113V8.887c0-.857.921-1.4 1.671-.983l5.603 3.113Z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    ) : (
                        PlayerStatus === "PLAYING" ? (
                            <div onClick={HandlePauseButtonClick}>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="hover:scale-110 size-12 cursor-pointer">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM9 8.25a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75h.75a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75H9Zm5.25 0a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75H15a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75h-.75Z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        ) : (
                            <div onClick={HandlePlayButtonClick}>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="hover:scale-110 size-12 cursor-pointer">
                                    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM9 8.25a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75h.75a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75H9Zm5.25 0a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75H15a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75h-.75Z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        )
                    )}
                </div>
                <img className={"aspect-video max-w-md rounded-medium cursor-pointer " + (PlayerStatus === "WAITING" ? "block" : "hidden")} ref={VideoImageRef} src={ImageURL}></img>
                <video ref={VideoRef} className={PlayerStatus === "WAITING" ? "hidden" : "pointer-events-none"}>
                    <source ref={VideoSrc}></source>
                </video>
            </div>
        </div >
    )
}