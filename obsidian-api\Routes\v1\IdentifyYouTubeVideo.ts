import { Request, Response } from "express"
import { YouTube } from "../../Index"
import axios from "axios";


/**
 * @function IdentifyYouTubeVideo
 * <AUTHOR> (@honzacies)
 * @description Gets everything about the requested YouTube video and returns it.
 * @returns {void}
 */
export default async function IdentifyYouTubeVideo(Request: Request, Response: Response) {
    if (Request.query.id) {
        const VideoData = await YouTube.Session?.getInfo(Request.query.id as string)
        const StreamingData = await YouTube.Session?.getStreamingData(Request.query.id as string)

        if (VideoData) {
            return Response.status(200).send({ data: VideoData })
        } else {
            return Response.status(500).send({ error: "error.youtube.no_data" })
        }
    } else {
        return Response.status(400).send({ error: "error.youtube.id_not_found" })
    }
}