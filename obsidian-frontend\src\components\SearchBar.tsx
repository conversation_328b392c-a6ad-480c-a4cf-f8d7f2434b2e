import { Input } from "@heroui/input";
import { motion } from "framer-motion";
import { createRef, useState } from "react";
import { Button } from "@heroui/button";
import { createRoot } from "react-dom/client";
import { <PERSON>rror<PERSON>enderer } from "./ErrorRenderer";
import YouTubeVideo from "./YouTubeVideo";

interface ErrorType {
  error: string;
  link?: string;
}

export function SearchBar() {
  const [buttonDisabled, setButtonDisabled] = useState(true);
  const [Value, SetValue] = useState("");
  const [IsEmpty, SetIsEmpty] = useState(true);

  const InputRef = createRef<HTMLInputElement>();

  async function handleInput() {
    if (InputRef.current) {
      if (InputRef.current.value.trim().length === 0) {
        setButtonDisabled(true);
        SetValue("");
        SetIsEmpty(true);
      } else {
        setButtonDisabled(
          !/^(https?:\/\/)?[\w.-]+\.[a-z]{2,}([\/?#].*)?$/i.test(
            InputRef.current.value,
          ),
        );
        SetIsEmpty(false);
        SetValue(InputRef.current.value);
      }
    }
  }

  async function handleInputClear() {
    if (InputRef.current) {
      setButtonDisabled(true);
    }
  }

  async function HandleButtonPress() {
    if (Value.trim().length === 0) {
      return console.log("empty");
    } else {
      const platform = window.obsidian.identifyUrl(Value)
      const info = await window.obsidian.fetchContent(platform, { url: Value });

      if (info.error) {
        const errorContainer = document.getElementById("error-container");
        const typedInfo = info as ErrorType

        if (errorContainer !== null) {
          createRoot(errorContainer).render(<ErrorRenderer error={typedInfo.error} />)
        };
      } else {
        const errorContainer = document.getElementById("error-container");
        console.log(info)
        if (errorContainer !== null) {
          return createRoot(errorContainer).render(<YouTubeVideo response={info.data} streaming={info.streaming} />)
        }
        return;
      }
    }
  }

  return (
    <div className="flex flex-row gap-1">
      <Input
        isClearable
        className="select-none"
        classNames={{ input: "select-text" }}
        placeholder="paste the link right here..."
        startContent={
          <motion.svg
            animate={{
              opacity: IsEmpty ? [0.6, 1, 0.6] : 1,
            }}
            className="size-5 fill-current text-black/50"
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12.232 4.232a2.5 2.5 0 0 1 3.536 3.536l-1.225 1.224a.75.75 0 0 0 1.061 1.06l1.224-1.224a4 4 0 0 0-5.656-5.656l-3 3a4 4 0 0 0 .225 5.865.75.75 0 0 0 .977-1.138 2.5 2.5 0 0 1-.142-3.667l3-3Z" />
            <path d="M11.603 7.963a.75.75 0 0 0-.977 1.138 2.5 2.5 0 0 1 .142 3.667l-3 3a2.5 2.5 0 0 1-3.536-3.536l1.225-1.224a.75.75 0 0 0-1.061-1.06l-1.224 1.224a4 4 0 1 0 5.656 5.656l3-3a4 4 0 0 0-.225-5.865Z" />
          </motion.svg>
        }
        variant="bordered"
        onClear={handleInputClear}
        onInput={handleInput}
        ref={InputRef}
      />
      <div className="flex items-center">
        <Button
          isIconOnly
          className="border-default-200 border-medium w-[40px] h-[40px] rounded-medium cursor-pointer"
          size="sm"
          variant="light"
          onPress={HandleButtonPress}
          disabled={buttonDisabled}
        >
          <svg
            className="size-4 stroke-default-800"
            fill="none"
            strokeWidth="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </Button>
      </div>
    </div>
  );
}
